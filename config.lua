Config = {}

-- إعدادات عامة
Config.Debug = true
Config.Language = 'ar' -- العربية

-- إعدادات Anti-Cheat
Config.AntiCheat = {
    Enabled = true,
    CheckInterval = 5000, -- كل 5 ثواني
    MaxWarnings = 3,
    BanDuration = 86400, -- 24 ساعة بالثواني
    
    -- فحص السرعة
    SpeedCheck = {
        Enabled = true,
        MaxSpeed = 200.0, -- أقصى سرعة مسموحة
        MaxSpeedInAir = 300.0
    },
    
    -- فحص الأسلحة
    WeaponCheck = {
        Enabled = true,
        BlacklistedWeapons = {
            'WEAPON_RAILGUN',
            'WEAPON_FIREWORK',
            'WEAPON_MINIGUN'
        }
    },
    
    -- فحص الموقع
    PositionCheck = {
        Enabled = true,
        MaxHeight = 1000.0,
        CheckTeleport = true,
        MaxTeleportDistance = 500.0
    },
    
    -- فحص الصحة والدرع
    HealthCheck = {
        Enabled = true,
        MaxHealth = 200,
        MaxArmour = 100
    }
}

-- إعدادات حماية DDoS
Config.DDoSProtection = {
    Enabled = true,
    MaxRequestsPerSecond = 10,
    MaxRequestsPerMinute = 100,
    BanDuration = 3600, -- ساعة واحدة
    WhitelistedIPs = {
        '127.0.0.1',
        -- أضف IPs موثوقة هنا
    }
}

-- إعدادات Whitelist
Config.Whitelist = {
    Enabled = false, -- فعل إذا كنت تريد whitelist فقط
    AllowedIdentifiers = {
        -- 'steam:110000000000000',
        -- 'license:1234567890abcdef'
    }
}

-- إعدادات Blacklist
Config.Blacklist = {
    Enabled = true,
    BannedIdentifiers = {
        -- 'steam:110000000000000'
    },
    BannedIPs = {
        -- '*************'
    }
}

-- إعدادات التسجيل
Config.Logging = {
    Enabled = true,
    LogToFile = true,
    LogToConsole = true,
    LogToDiscord = false,
    DiscordWebhook = '', -- ضع webhook هنا
    
    LogTypes = {
        Connections = true,
        Kicks = true,
        Bans = true,
        Warnings = true,
        Suspicious = true
    }
}

-- رسائل النظام
Config.Messages = {
    ['ar'] = {
        kicked_cheating = 'تم طردك من السيرفر بسبب استخدام الغش',
        banned_cheating = 'تم حظرك من السيرفر بسبب استخدام الغش',
        suspicious_activity = 'تم اكتشاف نشاط مشبوه',
        ddos_protection = 'تم حظرك مؤقتاً بسبب كثرة الطلبات',
        not_whitelisted = 'أنت غير مسجل في القائمة البيضاء',
        blacklisted = 'أنت محظور من السيرفر'
    },
    ['en'] = {
        kicked_cheating = 'You have been kicked for cheating',
        banned_cheating = 'You have been banned for cheating',
        suspicious_activity = 'Suspicious activity detected',
        ddos_protection = 'You have been temporarily banned for too many requests',
        not_whitelisted = 'You are not whitelisted',
        blacklisted = 'You are blacklisted from this server'
    }
}

-- إعدادات الأحداث المحمية
Config.ProtectedEvents = {
    'esx:getSharedObject',
    'bank:deposit',
    'bank:withdraw',
    'esx_ambulancejob:revive',
    -- أضف الأحداث المهمة هنا
}
